import rest, { restOrigin } from "@urls/axios";
import urls from "@/urls/url";

export function getNurseAssets(params = { page: 1, pageSize: 1000, isValid: true }) {
  return restOrigin.get("/hcapmassetservice/api/apm/asset/assetinfos/homeNurseAssetCount", { ...params });
}

export function getNurseAssetsInMaintain(params = { page: 0, pageSize: 1000 }) {
  return restOrigin.get("/hcapmreportservice/api/apm/report/nurseSite/assetsInMaintain_v2", { ...params });
}

export function getNurseAvgResponseTime() {
  return rest.get("/hcapmreportservice/api/apm/report/nurseSite/avgResponseTime");
}
//get socket id
export const getClientId = async () => {
  return rest.get(urls.getClientId);
};

// 创建供应商
export const creatVender = async data => {
  return rest.post(urls.venderCreat, data);
};

// 获取代办事件列表
export const getTodoList = async data => {
  return rest.get(urls.venderAgencyList, data);
};
// 获取代办事件详情
export const getTodoItem = async data => {
  return rest.get(`${urls.venderAgencyGet}/${data.id}`, {
    module: data.module,
  });
};
// 保存代办事件
export const saveTodoItem = async data => {
  return rest.put(`${urls.venderAgencySave}`, data);
};

// 获取采购信息
export const getPurchaseInfo = async data => {
  return rest.get(urls.purchaseInfo, data);
};

// 提交标书
export const postPurchase = async data => {
  return rest.post(urls.purchaseVender, data);
};

// 获取租户列表
export const getTenantList = async data => {
  return rest.get(urls.tenantList, data);
};

// 获取合同列表
export const getPurchaseList = async data => {
  return rest.get(urls.purchaseList, data);
};

// 资质管理更新
export const updateVenderCompany = async data => {
  return rest.put(urls.venderCompany, data);
};

// 添加、编辑供应商联系人
export const updateContactPersons = async data => {
  return rest.put(`${urls.contactPersons}/${data.venderCompanyId}`, data);
};
//删除供应商联系人
export const deleteContactPersons = async data => {
  return rest.delete(`${urls.deleteContactPersons}/${data.venderCompanyId}/${data.contactPersonsId}`);
};

// 供应商未绑定采购单数据
export const getUnPurchaseInfo = async data => {
  return rest.get(urls.unPurchaseInfo, data);
};

// 获取合同列表
export const getContractList = async data => {
  return rest.get(urls.contractList, data);
};

// 获取合同详情
export const getContractDetail = async id => {
  return rest.get(`${urls.contractInfo}/${id}`);
};

// 获取院区
export const getSiteList = async data => {
  return rest.get(`${urls.contractSiteList}`, data);
};

// 暂存合同
export const saveContractDetail = async data => {
  return rest.put(urls.contractSave, data);
};
// 提交审核合同
export const submitAuditContract = async id => {
  return restOrigin.put(`${urls.submitAudit}/${id}`);
};
//到货清单
export const getArrivalList = async data => {
  return rest.get(`${urls.arrivalListList}`, data);
};
//到货清单里合同列表
export const getArrivalContractLis = async data => {
  return rest.get(`${urls.arrivalContractLis}`, data);
};
//到货清单里合同订购设备列表
export const getArrivalContractItemList = async data => {
  return rest.get(`${urls.arrivalContractItemList}`, data);
};
//到货清单保存
export const saveArrival = async data => {
  return rest.put(`${urls.arrivalSupplier}/${data.id}`, data);
};
//到货清单删除
export const deleteArrival = async deleteId => {
  return rest.delete(`${urls.arrivalSupplier}/${deleteId}`);
};
//设备管理
export const getArrivalListItems = async data => {
  return rest.get(`${urls.arrivalListItemList}`, data);
};
// 获取用户列表
export const getVenderList = async data => {
  return rest.get(urls.venderList, data);
};
//保存编辑信息
export const saveVender = async data => {
  return rest.post(urls.saveVender, data);
};
//删除用户
export const deleteVender = async deleteId => {
  return rest.delete(`${urls.deleteVender}/${deleteId}/vender`);
};
/*获取登录用户信息----奇怪的接口*/
export const getVenderInfo = async token => {
  return rest.post(`${urls.venderInfo}?token=${token}`);
};

//科室
export const getDeptList = async data => {
  return rest.get(`${urls.deptList}`, data);
};

//供应商科室
export const getVenderDeptList = async data => {
  return rest.get(`${urls.venderDeptList}`, data);
};

//
export const getByKeyAndSite = async data => {
  return rest.get(`${urls.byKeyAndSite}`, data);
};

export const getCustomConfig = async data => {
  return rest.get(`${urls.customConfig}`, data);
};
//设备管理列表
export const getAssetList = async data => {
  return rest.get(`${urls.assetList}`, data);
};
//保存设备信息
export const saveAssetItem = async data => {
  return rest.put(`${urls.saveAssetinfos}/${data.id}?tenantUid=${data.tenantUID}`, data);
};
//获取 i18 数据
export const getI8nMessages = async data => {
  return rest.get(`${urls.i8nMessages}`, data);
};
//满意度调查表看板
export const getSurveyPushData = async data => {
  return rest.get(`${urls.surveyPushData}`, data);
};
export const getSurveyResultData = async data => {
  return rest.get(`${urls.surveyResultData}`, data);
};
//设备综合绩效项目

/*基本信息*/
export const getAssetinfos = async assetUid => {
  return rest.get(`${urls.assetinfos}/${assetUid}`);
};
/*绩效概览*/
export const getPayback = async data => {
  return rest.get(`${urls.paybackPeriod}`, data);
};
/*绩效概览*/
export const getPerformanceInfo = async data => {
  return rest.get(`${urls.performanceInfo}`, data);
};
/*收支详情*/
export const getStatistics = async data => {
  return rest.get(`${urls.statistics}`, data);
};
/*质控情况*/
export const getQualityControl = async data => {
  return rest.get(`${urls.qualityControl}`, data);
};
/*收费项目占比*/
export const getsfxmAmountPercentage = async data => {
  return rest.get(`${urls.sfxmAmountPercentage}`, data);
};
/*数据配置*/
export const saveReportConfig = async data => {
  return rest.post(`${urls.saveReportConfig}?assetUid=${data.id}`, data.data);
};
/*get数据配置*/
export const getReportConfig = async data => {
  return rest.get(`${urls.getReportConfig}`, data);
};

//科室设备效益综合分析

/*绩效概览 post*/
export const getDeptAssetPerformanceInfo = async data => {
  return rest.post(urls.deptAssetPerformanceInfo, data);
};
/*设备分布 post*/
export const getDeptAssetDistributio = async data => {
  return rest.post(urls.deptAssetDistributio, data);
};
/*收费项目金额占比 post*/
export const getDeptAssetSfxmAmount = async data => {
  return rest.post(urls.deptAssetSfxmAmountPercentage, data);
};
/*支出情况统计 post*/
export const getDeptAssetExpensesInfo = async data => {
  return rest.post(urls.deptAssetExpensesInfo, data);
};
/*收支趋势、检查人次、完好率趋势 post*/
export const getDeptAssetIaEInfo = async data => {
  return rest.post(urls.deptAssetIncomeAndExpensesInfo, data);
};
/*周检查人次日期分布*/
export const getDeptAssetWeekCheckNumber = async data => {
  return rest.post(urls.deptAssetWeekCheckNumber, data);
};
/*维修保养工单量分析*/
export const getDeptAssetWsorkOrderPmOrderQ = async data => {
  return rest.post(urls.deptAssetWsorkOrderPmOrderQuantity, data);
};
/*单台设备绩效分析列表析*/
export const getDeptAssetPerformance = async data => {
  return rest.post(urls.deptAssetPerformance, data);
};
/*设备标签*/
export const getDeptAssetTag = async data => {
  return rest.get(`${urls.deptAssetAssetTag}`, data);
};
/*获取科室配置*/
export const getDeptAssetReportConfig = async () => {
  return rest.get(`${urls.deptAssetReportConfig}`);
};
/*保存科室配置*/
export const saveDeptAssetReportConfig = async data => {
  return rest.post(`${urls.deptAssetReportConfigSave}`, data);
};

/*——————————质控日历接口————————————*/

/*获取日历_年*/
export const getCalendarYear = async data => {
  return rest.get(`${urls.calendarYear}`, data);
};
/*获取日历_月*/
export const getCalendarMonth = async data => {
  return rest.get(`${urls.calendarMonth}`, data);
};
/*获取日历_月—临床*/
export const getCalendarMonthNurse = async data => {
  return rest.get(`${urls.calendarMonthNurse}`, data);
};
/*获取日历_月—预防维护*/
export const getCalendarMonthPmOrder = async data => {
  return rest.get(`${urls.calendarMonthPmOrder}`, data);
};
/*获取日历_月—计量*/
export const getCalendarMonthMeasuring = async data => {
  return rest.get(`${urls.calendarMonthMeasuring}`, data);
};

/*——————————质控数据接口————————————*/

export const getCalendarDataAll = async data => {
  return rest.get(`${urls.calendarDataAll}`, data);
};
/*获取日历_月*/
export const getCalendarDataNursel = async data => {
  return rest.get(`${urls.calendarDataNursel}`, data);
};
/*获取日历_月—预防维护*/
export const getCalendarDataPmOrder = async data => {
  return rest.get(`${urls.calendarDataPmOrder}`, data);
};
/*获取日历_月—计量*/
export const getCalendarDataMeasuring = async data => {
  return rest.get(`${urls.calendarDataMeasuring}`, data);
};

/*——————————质控大屏第二期（放射科）数据接口————————————*/
/*质控设备数量*/
export const getZkDashboardAssetCount = async data => {
  return rest.get(`${urls.zkDashboardAssetCount}`, data);
};
/*质控完成工单量*/
export const getZkPmFinishedCount = async data => {
  return rest.get(`${urls.zkPmFinishedCount}`, data);
};
/*开机率*/
export const getZkOperationRate = async data => {
  return rest.get(`${urls.zkOperationRate}`, data);
};
/*质控闭环数据（预防维护工单维度）*/
export const getZkWorkOrderDataCount = async data => {
  return rest.get(`${urls.zkWorkOrderDataCount}`, data);
};
/*日常质控*/
export const getZkOrderDetail = async data => {
  return rest.get(`${urls.zkOrderDetail}`, data);
};
/*预防维护*/
export const getZkPmDetail = async data => {
  return rest.get(`${urls.zkPmDetail}`, data);
};
/*故障排名*/
export const getZkWorkOrder = async data => {
  return rest.get(`${urls.zkWorkOrder}`, data);
};
