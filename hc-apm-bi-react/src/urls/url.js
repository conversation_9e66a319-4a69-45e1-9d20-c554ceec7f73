const userAccounts = "/api/apm/security/userAccounts";
const objects = "/zuul/hcapmobjecthubservice/api/apm/objectHub/objects";
const masterData = "/hcapmmasterdataservice/api/apm/masterData";
const qrCodes = masterData + "/qrcodes";

const gateway = {
  /** gateway */
  basicAuth: userAccounts + "/authenticateBasic",
  myself: userAccounts + "/myself",
  socketLogin: "/api/apm/qrLogin/noticeQrCodeLogin",
  /** master data */
  i18n: masterData + "/i8nMessages",
  qrCodeRule: masterData + "/qrCodeRuleConfig/qrCode",
  qrCodes,
  qrCodeLibs: qrCodes + "/qrCode",
  qrCodeImg: qrCodes + "/qrCode/image",
  qrCodeScan: qrCodes + "/qrCodeScan",
  /** object */
  obj: objects + "/",
  objUrl: objects + "/urlFile",
  objSingle: objects + "/single",
  objMutiple: objects + "/multiple",
  objDownload: objects + "/download",
  /** download **/
  getAuthCode: "/hcapmobjecthubservice/api/apm/objectHub/objects/getAuthCode",
  getClientId: "/api/apm/qrLogin/getClientId",
  downloadByAuthCode: "/hcapmobjecthubservice/api/apm/objectHub/objects/downloadByAuthCode",
  i8nMessages: "hcapmmasterdataservice/api/apm/masterData/i8nMessages/batch",
  /*创建供应商*/
  venderCreat: "hcapmassetservice/api/apm/asset/vender",
  /*采购信息*/
  purchaseInfo: "hcapmassetservice/api/apm/asset/purchase/purchaseInfo",
  /*标书提交*/
  purchaseVender: "hcapmassetservice/api/apm/asset/purchase/purchaseVender",
  /*租户列表*/
  tenantList: "hcapmassetservice/api/apm/asset/tenantList",
  /*投标管理*/
  purchaseList: "hcapmassetservice/api/apm/asset/purchase/getPurchaseInfoByVender",
  unPurchaseInfo: "hcapmassetservice/api/apm/asset/purchase/getUnPurchaseInfoByVender",
  /* 代办事件列表：*/
  venderAgencyList: "hcapmassetservice/api/apm/asset/venderAgency/list",
  /* 查看代办事件：*/
  venderAgencyGet: "hcapmassetservice/api/apm/asset/venderAgency/getById",
  /* 保存代办事件：*/
  venderAgencySave: "hcapmassetservice/api/apm/asset/venderNotice/save",

  /*资质管理更新*/
  venderCompany: "hcapmassetservice/api/apm/asset/venderCompany/save",
  // 合同
  /*保存*/
  contractSave: "hcapmassetservice/api/apm/asset/contractInfo/save",
  /*审核*/
  submitAudit: "hcapmassetservice/api/apm/asset/contractInfo/submitAudit",
  /*合同列表*/
  contractList: "hcapmassetservice/api/apm/asset/contractInfo/list",
  /*合同详情*/
  contractInfo: "hcapmassetservice/api/apm/asset/contractInfo/get",
  /*院区列表*/
  contractSiteList: "hcapmassetservice/api/apm/asset/contractInfo/siteList",
  /*到货清单*/
  arrivalListList: "hcapmassetservice/api/apm/arrival/arrivalListList",
  /*到货清单里合同列表*/
  arrivalContractLis: "hcapmassetservice/api/apm/asset/contractInfo/getArrivalContractList",
  /*到货清单合同订购设备列表*/
  arrivalContractItemList: "hcapmassetservice/api/apm/asset/contractInfo/getArrivalContractItemList",
  /*到货清单保存*/
  arrivalSupplier: "hcapmassetservice/api/apm/arrival/supplier",
  /*设备列表*/
  arrivalListItemList: "hcapmassetservice/api/apm/arrival/arrivalListItemList",
  /*vender用户列表*/
  venderList: "hcapmassetservice/api/apm/asset/venderList",
  /*保存vender item信息*/
  saveVender: "hcapmassetservice/api/apm/asset/saveVender",
  /*删除vender item信息*/
  deleteVender: "hcapmassetservice/api/apm/asset",
  /*获取用户信息*/
  venderInfo: "api/apm/security/userAccounts/getVenderInfoByToken",
  /*添加编辑供应商联系人*/
  contactPersons: "/api/apm/asset/venderCompany/saveContactPerson",
  /*删除应商联系人*/
  deleteContactPersons: "/api/apm/asset/venderCompany/deleteContactPerson",

  /*科室*/
  deptList: "hcapmreportservice/api/apm/deptAsset/performance/data/deptList",
  /*供应商科室*/
  venderDeptList: "hcapmassetservice//api/apm/asset/contractInfo/deptList",

  /*getByKeyAndSite*/
  byKeyAndSite: "hcapmmasterdataservice/api/apm/masterData/configItem/getByKeyAndSite",
  /*设备自定义显示字段*/
  customConfig: "hcapmmasterdataservice/api/apm/masterData/CustomConfig",
  /*设备列表*/
  assetList: "hcapmassetservice/api/apm/asset/assetinfos/getSupplierEmPowermentList",
  /*保存设备信息*/
  saveAssetinfos: "hcapmassetservice/api/apm/asset/assetinfos",
  //满意度调查表看板
  surveyPushData: "hcapmmasterdataservice/api/apm/masterData/survey/dashboard/pushForm",
  surveyResultData: "hcapmmasterdataservice/api/apm/masterData/survey/dashboard/resultForm",
  surveyResultDownloadUrl: "hcapmmasterdataservice/api/apm/masterData/survey/dashboard/resultDownload",
  surveyPushDownloadUrl: "hcapmmasterdataservice/api/apm/masterData/survey/dashboard/pushDownload",

  // 设备综合绩效
  /*设备基本信息*/
  assetinfos: "hcapmassetservice/api/apm/asset/assetinfos/byUid",
  /*绩效概览*/
  performanceInfo: "hcapmreportservice/api/apm/iot/performance/data/info",
  /*投资回收期*/
  paybackPeriod: "hcapmreportservice/api/apm/iot/performance/data/paybackPeriod",
  /*收支详情*/
  statistics: "hcapmreportservice/api/apm/iot/performance/data/statistics/month",
  /*质控情况*/
  qualityControl: "hcapmreportservice/api/apm/iot/performance/data/qualityControl",
  /*收费项目占比*/
  sfxmAmountPercentage: "hcapmreportservice/api/apm/iot/performance/data/his/sfxmAmountPercentage",
  /*数据配置*/
  saveReportConfig: "hcapmreportservice/api/apm/iot/performance/data/saveReportConfig",
  getReportConfig: "hcapmreportservice/api/apm/iot/performance/data/getReportConfig",

  //科室设备效益综合分析
  /*绩效概览 post*/
  deptAssetPerformanceInfo: "hcapmreportservice/api/apm/deptAsset/performance/data/info",
  /*设备分布 post*/
  deptAssetDistributio: "hcapmreportservice/api/apm/deptAsset/performance/data/assetDistributio",
  /*收费项目金额占比 post*/
  deptAssetSfxmAmountPercentage: "hcapmreportservice/api/apm/deptAsset/performance/data/sfxmAmountPercentage",
  /*支出情况统计 post*/
  deptAssetExpensesInfo: "hcapmreportservice/api/apm/deptAsset/performance/data/expensesInfo",
  /*收支趋势、检查人次、完好率趋势 post*/
  deptAssetIncomeAndExpensesInfo: "hcapmreportservice/api/apm/deptAsset/performance/data/incomeAndExpensesInfo/month",
  /*周检查人次日期分布 post*/
  deptAssetWeekCheckNumber: "hcapmreportservice/api/apm/deptAsset/performance/data/weekCheckNumber",
  /*维修保养工单量分析 post*/
  deptAssetWsorkOrderPmOrderQuantity: "hcapmreportservice/api/apm/deptAsset/performance/data/workOrderPmOrderQuantity",
  /*维修保养工单量分析 post*/
  deptAssetPerformance: "hcapmreportservice/api/apm/deptAsset/performance/data/assetPerformance",
  /*设备标签 get*/
  deptAssetAssetTag: "hcapmreportservice/api/apm/deptAsset/performance/data/getAssetTag",
  /*获取科室配置 get*/
  deptAssetReportConfig: "hcapmreportservice/api/apm/deptAsset/performance/data/getReportConfig",
  /*保存科室配置 post*/
  deptAssetReportConfigSave: "hcapmreportservice/api/apm/deptAsset/performance/data/saveReportConfig",

  //质控日历
  /*年——日历*/
  calendarYear: "hcapmreportservice/api/apm/report/qc/calendar/year/all",
  /*月——日历*/
  calendarMonth: "hcapmreportservice/api/apm/report/qc/calendar/month/all",
  /*月——日历—临床*/
  calendarMonthNurse: "hcapmreportservice/api/apm/report/qc/calendar/month/nurse",
  /*月——日历—预防维护*/
  calendarMonthPmOrder: "hcapmreportservice/api/apm/report/qc/calendar/month/pmOrder",
  /*月——日历—计量*/
  calendarMonthMeasuring: "hcapmreportservice/api/apm/report/qc/calendar/month/measuring",
  //质控数据
  /*数据——全部*/
  calendarDataAll: "hcapmreportservice/api/apm/report/qc/calendar/data/all",
  /*数据——临床助手*/
  calendarDataNursel: "hcapmreportservice/api/apm/report/qc/calendar/data/nurse",
  /*数据——预防维护*/
  calendarDataPmOrder: "hcapmreportservice/api/apm/report/qc/calendar/data/pmOrder",
  /*数据——计量*/
  calendarDataMeasuring: "hcapmreportservice/api/apm/report/qc/calendar/data/measuring",

  // 质控大屏

  /*质控设备数量*/
  zkDashboardAssetCount: "hcapmreportservice/api/apm/report/zkDashboard2/assetCount",
  /*质控完成工单量*/
  zkPmFinishedCount: "hcapmreportservice/api/apm/report/zkDashboard2/zkPmFinishedCount",
  /*开机率*/
  zkOperationRate: "hcapmreportservice/api/apm/report/zkDashboard2/zkOperationRate",
  /*质控闭环数据（预防维护工单维度）*/
  zkWorkOrderDataCount: "hcapmreportservice/api/apm/report/zkDashboard2/zkWorkOrderDataCount",
  /*日常质控*/
  zkOrderDetail: "hcapmreportservice/api/apm/report/zkDashboard2/zkOrderDetail",
  /*预防维护*/
  zkPmDetail: "hcapmreportservice/api/apm/report/zkDashboard2/zkPmDetail",
  /*故障排名*/
  zkWorkOrder: "hcapmreportservice/api/apm/report/zkDashboard2/zkWorkOrder",
};

let urls = {};
const domains = { gateway };
for (let domain in domains) {
  const obj = domains[domain];
  urls = Object.assign(urls, obj);
}
export default urls;
