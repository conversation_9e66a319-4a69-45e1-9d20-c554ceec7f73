.certification {
    height: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;

    .company-name {
        margin: 20px 0;
    }

    .ant-descriptions {
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 15px;

        .ant-checkbox-wrapper-disabled {
            cursor: default;
        }

        .ant-checkbox-disabled {
            cursor: default;

            +span {
                color: #333;
                cursor: default;
            }

            .ant-checkbox-inner {
                background-color: #fff;
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
                background-color: #1890ff;
                border-color: #1890ff !important;

                &::after {
                    border-color: #f5f5f5;
                }
            }
        }

        .ant-descriptions-item-container {
            align-items: center;
        }

        .download-link {
            color: #1890ff;
            cursor: pointer;

            .anticon-file-done {
                margin-right: 5px;
            }
        }
    }

    .contact-persons {
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 15px;

        .ant-table-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    form {
        padding: 0;

        .form__label {
            min-width: 155px;
            justify-content: flex-end;
            padding-right: 10px;
        }

        .btn-group {
            text-align: center;
        }
    }

    .Upload-box {
        display: flex;
    }

    .ant-upload-list {

        .ant-upload-text-icon,
        .ant-upload-list-item-progress {
            display: none;
        }
    }
}