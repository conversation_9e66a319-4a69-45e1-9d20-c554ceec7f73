<template>
  <a-layout-header class="app-header">
    <div class="nav-container">
      <div class="logo">
        <h2>智能旅游助手</h2>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        mode="horizontal"
        class="nav-menu"
        @click="handleMenuClick"
      >
        <a-menu-item key="home">
          <template #icon>
            <HomeOutlined />
          </template>
          首页
        </a-menu-item>
        <a-menu-item key="travel">
          <template #icon>
            <EnvironmentOutlined />
          </template>
          旅游规划
        </a-menu-item>
        <a-menu-item key="analyseDoc">
          <template #icon>
            <FileExcelOutlined />
          </template>
          Excel分析
        </a-menu-item>
        <a-menu-item key="analyse-enhanced">
          <template #icon>
            <FileExcelOutlined />
          </template>
          高级Excel分析
        </a-menu-item>
        <a-menu-item key="about">
          <template #icon>
            <InfoCircleOutlined />
          </template>
          关于
        </a-menu-item>
      </a-menu>
    </div>
  </a-layout-header>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  EnvironmentOutlined, 
  HomeOutlined, 
  InfoCircleOutlined,
  FileExcelOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

const selectedKeys = ref([])

// 根据当前路由设置选中的菜单项
const updateSelectedKeys = () => {
  const routeName = route.name
  if (routeName) {
    selectedKeys.value = [routeName]
  }
}

// 监听路由变化
watch(route, updateSelectedKeys, { immediate: true })

// 处理菜单点击
const handleMenuClick = ({ key }) => {
  router.push({ name: key })
}
</script>

<style scoped>
.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 64px;
  line-height: 64px;
}

.nav-container {
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 100%;
}

.logo h2 {
  margin: 0;
  color: #1890ff;
  font-weight: 600;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
  min-width: 400px;
  justify-content: flex-end;
}

.nav-menu .ant-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .logo h2 {
    font-size: 18px;
  }
  
  .nav-menu {
    min-width: auto;
  }
}
</style>
